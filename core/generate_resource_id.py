from ulid import ULID
import secrets
import time
from typing import Optional

def generate_resource_ulid(prefix: Optional[str] = None) -> str:
    """
    生成资源ULID标识符
    
    Args:
        prefix: 可选的前缀，如 'res_', 'user_', 'order_' 等
    
    Returns:
        str: 生成的ULID字符串，如果有前缀则为 'prefix_ulid'
    
    Examples:
        >>> generate_resource_ulid()
        '01HQZX8K9G7QJXVN2M4P5R6S7T'
        
        >>> generate_resource_ulid('res')
        'res_01HQZX8K9G7QJXVN2M4P5R6S7T'
        
        >>> generate_resource_ulid('user')
        'user_01HQZX8K9G7QJXVN2M4P5R6S7T'
    """
    try:
        # 生成ULID
        ulid = str(ULID())
        
        # 如果有前缀，添加前缀
        if prefix:
            # 确保前缀格式正确（移除可能的下划线并重新添加）
            clean_prefix = prefix.rstrip('_')
            return f"{clean_prefix}_{ulid}"
        
        return ulid
        
    except Exception as e:
        # 如果ULID生成失败，使用备用方案
        # 使用时间戳 + 随机数生成类似ULID的标识符
        timestamp = int(time.time() * 1000)  # 毫秒时间戳
        random_part = secrets.token_urlsafe(16)[:16]  # 16字符随机部分
        
        backup_id = f"{timestamp:013d}{random_part}".upper()
        
        if prefix:
            clean_prefix = prefix.rstrip('_')
            return f"{clean_prefix}_{backup_id}"
        
        return backup_id


def generate_batch_ulids(count: int, prefix: Optional[str] = None) -> list[str]:
    """
    批量生成ULID
    
    Args:
        count: 生成数量
        prefix: 可选的前缀
    
    Returns:
        list[str]: ULID列表
    
    Examples:
        >>> ulids = generate_batch_ulids(5, 'res')
        >>> len(ulids)
        5
        >>> all(ulid.startswith('res_') for ulid in ulids)
        True
    """
    return [generate_resource_ulid(prefix) for _ in range(count)]


def validate_ulid_format(ulid_string: str, prefix: Optional[str] = None) -> bool:
    """
    验证ULID格式是否正确
    
    Args:
        ulid_string: 要验证的ULID字符串
        prefix: 期望的前缀（可选）
    
    Returns:
        bool: 格式是否正确
    
    Examples:
        >>> validate_ulid_format('01HQZX8K9G7QJXVN2M4P5R6S7T')
        True
        
        >>> validate_ulid_format('res_01HQZX8K9G7QJXVN2M4P5R6S7T', 'res')
        True
        
        >>> validate_ulid_format('invalid_ulid')
        False
    """
    try:
        # 如果期望有前缀
        if prefix:
            expected_prefix = f"{prefix.rstrip('_')}_"
            if not ulid_string.startswith(expected_prefix):
                return False
            # 移除前缀后验证ULID部分
            ulid_part = ulid_string[len(expected_prefix):]
        else:
            ulid_part = ulid_string
        
        # 验证ULID格式
        # ULID应该是26个字符，只包含Crockford Base32字符
        if len(ulid_part) != 26:
            return False
        
        # Crockford Base32字符集（不包含I, L, O, U）
        valid_chars = set('0123456789ABCDEFGHJKMNPQRSTVWXYZ')
        return all(c in valid_chars for c in ulid_part.upper())
        
    except Exception:
        return False


def extract_timestamp_from_ulid(ulid_string: str, prefix: Optional[str] = None) -> Optional[int]:
    """
    从ULID中提取时间戳
    
    Args:
        ulid_string: ULID字符串
        prefix: 前缀（如果有的话）
    
    Returns:
        Optional[int]: Unix时间戳（毫秒），如果解析失败返回None
    
    Examples:
        >>> timestamp = extract_timestamp_from_ulid('01HQZX8K9G7QJXVN2M4P5R6S7T')
        >>> isinstance(timestamp, int)
        True
    """
    try:
        # 移除前缀（如果有）
        if prefix:
            expected_prefix = f"{prefix.rstrip('_')}_"
            if ulid_string.startswith(expected_prefix):
                ulid_part = ulid_string[len(expected_prefix):]
            else:
                return None
        else:
            ulid_part = ulid_string
        
        # 使用ULID库解析时间戳
        ulid_obj = ULID.from_str(ulid_part)
        return int(ulid_obj.timestamp * 1000)  # 转换为毫秒
        
    except Exception:
        return None


# 使用示例和测试函数
def test_ulid_functions():
    """测试ULID相关函数"""
    print("=== ULID函数测试 ===")
    
    # 测试基本生成
    ulid1 = generate_resource_ulid()
    print(f"基本ULID: {ulid1}")
    
    # 测试带前缀生成
    ulid2 = generate_resource_ulid('res')
    print(f"带前缀ULID: {ulid2}")
    
    # 测试批量生成
    batch_ulids = generate_batch_ulids(3, 'user')
    print(f"批量ULID: {batch_ulids}")
    
    # 测试验证
    print(f"ULID1验证: {validate_ulid_format(ulid1)}")
    print(f"ULID2验证: {validate_ulid_format(ulid2, 'res')}")
    
    # 测试时间戳提取
    timestamp = extract_timestamp_from_ulid(ulid1)
    print(f"时间戳: {timestamp}")
    
    print("=== 测试完成 ===")

# 如果需要测试，取消下面的注释
# test_ulid_functions()